import os
import glob
import pandas as pd
import zipfile
import shutil
from datetime import datetime, date
from rich.progress import Progress, BarColumn, TextColumn, TimeElapsedColumn, TimeRemainingColumn
from rich.console import Console
from rich.panel import Panel
from rich.text import Text
import warnings

# Suppress pandas warnings
warnings.filterwarnings('ignore')

class BounceUnsubscribeCollector:
    def __init__(self):
        self.console = Console()
        self.base_path = r"H:\Master Bounces and Unsubs\Mathews Postpanel Unsubs\bounces"
        self.output_path = os.path.join(self.base_path, "Processed_Data")
        self.timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        self.date_str = date.today().strftime('%d-%m-%Y')

        # Ensure output directory exists
        os.makedirs(self.output_path, exist_ok=True)
        
    def create_progress_bar(self):
        """Create a progress bar with gradient colors"""
        return Progress(
            TextColumn("[bold blue]{task.description}"),
            BarColumn(bar_width=None, style="bar.back", complete_style="green", finished_style="bright_green"),
            "[progress.percentage]{task.percentage:>3.0f}%",
            TimeElapsedColumn(),
            TimeRemainingColumn(),
            console=self.console
        )
    
    def display_header(self, title):
        """Display a formatted header"""
        header_text = Text(title, style="bold magenta")
        self.console.print(Panel(header_text, expand=False))

    def archive_bounces_folder(self):
        """Archive the bounces folder to a zip file before processing"""
        self.console.print("[yellow]Archiving bounces folder...[/yellow]")

        if not os.path.exists(self.base_path):
            self.console.print(f"[red]Bounces folder not found: {self.base_path}[/red]")
            return None

        # Create archive filename with current date
        archive_name = f"Bounces {self.date_str}.zip"
        archive_path = os.path.join(os.path.dirname(self.base_path), archive_name)

        try:
            # Create zip archive
            with zipfile.ZipFile(archive_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # Walk through the bounces directory and add all files and folders
                for root, dirs, files in os.walk(self.base_path):
                    for file in files:
                        file_path = os.path.join(root, file)
                        # Create archive name relative to the bounces folder
                        arcname = os.path.relpath(file_path, self.base_path)
                        zipf.write(file_path, arcname)

                    # Add empty directories
                    for dir_name in dirs:
                        dir_path = os.path.join(root, dir_name)
                        if not os.listdir(dir_path):  # If directory is empty
                            arcname = os.path.relpath(dir_path, self.base_path) + '/'
                            zipf.writestr(arcname, '')

            self.console.print(f"[green]✓ Bounces folder archived successfully: {archive_path}[/green]")
            return archive_path

        except Exception as e:
            self.console.print(f"[red]Error archiving bounces folder: {str(e)}[/red]")
            return None

    def process_elasticemail_bounces(self):
        """Process ElasticEmail bounce data"""
        self.console.print("[yellow]Processing ElasticEmail bounce data...[/yellow]")

        folder_path = os.path.join(self.base_path, "ee", "bounces")
        if not os.path.exists(folder_path):
            self.console.print(f"[red]ElasticEmail bounces folder not found: {folder_path}[/red]")
            return None, None

        os.chdir(folder_path)
        csv_files = glob.glob('*.csv')

        if not csv_files:
            self.console.print(f"[yellow]No CSV files found in {folder_path}[/yellow]")
            return None, None

        # Concatenate all CSV files
        df_concat = pd.concat([
            pd.read_csv(f, on_bad_lines='skip', low_memory=False, dtype='unicode')
            for f in csv_files
        ], ignore_index=True)

        # Extract required columns - assuming bounce data has email and bounce type info
        if 'to' in df_concat.columns:
            df = df_concat[['to', 'eventtype', 'messagecategory']].copy()
            df.rename(columns={'to': 'Email'}, inplace=True)

            # Filter bounces
            soft_bounces = df[df['eventtype'].isin(['Bounced'])][['Email']].copy()
            hard_bounces = df[df['messagecategory'].isin(['NoMailbox'])][['Email']].copy()

        elif 'Email' in df_concat.columns:
            # Alternative column structure
            df = df_concat[['Email']].copy()
            # Assume all are hard bounces if no categorization available
            hard_bounces = df.copy()
            soft_bounces = pd.DataFrame(columns=['Email'])
        else:
            self.console.print(f"[yellow]Unknown column structure in ElasticEmail bounces[/yellow]")
            return None, None

        # Remove hard bounces from soft bounces
        soft_bounces = soft_bounces[~soft_bounces['Email'].isin(hard_bounces['Email'])]

        # Remove duplicates and add source
        soft_bounces.drop_duplicates(subset='Email', keep='first', inplace=True)
        hard_bounces.drop_duplicates(subset='Email', keep='first', inplace=True)

        soft_bounces['Source'] = 'ElasticEmail_SoftBounces'
        hard_bounces['Source'] = 'ElasticEmail_HardBounces'

        return soft_bounces, hard_bounces

    def process_elasticemail_unsubs(self):
        """Process ElasticEmail unsubscribe data"""
        self.console.print("[yellow]Processing ElasticEmail unsubscribe data...[/yellow]")

        folder_path = os.path.join(self.base_path, "ee", "unsubs")
        if not os.path.exists(folder_path):
            self.console.print(f"[red]ElasticEmail unsubs folder not found: {folder_path}[/red]")
            return None

        os.chdir(folder_path)
        csv_files = glob.glob('*.csv')

        if not csv_files:
            self.console.print(f"[yellow]No CSV files found in {folder_path}[/yellow]")
            return None

        # Concatenate all CSV files
        df_concat = pd.concat([
            pd.read_csv(f, on_bad_lines='skip', low_memory=False, dtype='unicode')
            for f in csv_files
        ], ignore_index=True)

        # Extract email column
        if 'to' in df_concat.columns:
            unsubscribes = df_concat[['to']].copy()
            unsubscribes.rename(columns={'to': 'Email'}, inplace=True)
        elif 'Email' in df_concat.columns:
            unsubscribes = df_concat[['Email']].copy()
        else:
            self.console.print(f"[yellow]Unknown column structure in ElasticEmail unsubs[/yellow]")
            return None

        # Remove duplicates and add source
        unsubscribes.drop_duplicates(subset='Email', keep='first', inplace=True)
        unsubscribes['Source'] = 'ElasticEmail_Unsubscribes'

        return unsubscribes
    
    def process_sendgrid_data(self):
        """Process SendGrid data"""
        self.console.print("[yellow]Processing SendGrid data...[/yellow]")

        folder_path = os.path.join(self.base_path, "sg")
        if not os.path.exists(folder_path):
            self.console.print(f"[red]SendGrid folder not found: {folder_path}[/red]")
            return None, None, None
            
        os.chdir(folder_path)
        csv_files = glob.glob('*.csv')
        
        if not csv_files:
            return None, None, None
            
        df_concat = pd.concat([pd.read_csv(f, on_bad_lines='skip') for f in csv_files], ignore_index=True)
        df = df_concat[['event', 'email', 'reason']].copy()
        df.rename(columns={'email': 'Email'}, inplace=True)
        
        # Filter unsubscribes
        unsubscribes = df[df['event'].isin(['spamreport', 'unsubscribed', 'complaint'])][['Email']].copy()
        
        # Filter bounces
        bounces = df[df['event'].isin(['bounce'])].copy()
        
        # Identify hard bounces by reason patterns
        hard_bounce_patterns = [
            'User unknown', 'service disabled', 'does not exist', 'Address unknown',
            'User not found', 'no mailbox', 'no mail-box', 'no mail',
            'unrecognized address', 'mailbox is unavailable', 'mailbox unavailable'
        ]
        
        hard_bounces = bounces[bounces['reason'].str.contains('|'.join(hard_bounce_patterns), case=False, na=False)][['Email']].copy()
        soft_bounces = bounces[~bounces['Email'].isin(hard_bounces['Email'])][['Email']].copy()
        
        # Remove duplicates and add source
        for df_type, source_name in [(unsubscribes, 'SendGrid_Unsubscribes'), 
                                     (soft_bounces, 'SendGrid_SoftBounces'), 
                                     (hard_bounces, 'SendGrid_HardBounces')]:
            df_type.drop_duplicates(subset='Email', keep='first', inplace=True)
            df_type['Source'] = source_name
        
        return unsubscribes, soft_bounces, hard_bounces
    


    def process_postpanel_data(self):
        """Process Postpanel unsubscriber data"""
        self.console.print("[yellow]Processing Postpanel data...[/yellow]")

        # Process MW data - now looking in the parent directory structure
        mw_folder = r"H:\Master Bounces and Unsubs\Mathews Postpanel Unsubs\mw"
        mw_unsubscribes = None

        if os.path.exists(mw_folder):
            os.chdir(mw_folder)
            csv_files = glob.glob('*.csv')
            self.console.print(f"[blue]Found {len(csv_files)} CSV files in MW folder[/blue]")

            if csv_files:
                df_list = []
                for f in csv_files:
                    try:
                        df = pd.read_csv(f, on_bad_lines='skip', low_memory=False, dtype='unicode')
                        self.console.print(f"[blue]Processing MW file {f}: {len(df)} rows, columns: {list(df.columns)}[/blue]")

                        # Check if required columns exist
                        if all(col in df.columns for col in ['email', 'status']):
                            df_list.append(df[['email', 'status']].copy())
                            self.console.print(f"[green]✓ Added MW file {f}: {len(df)} rows[/green]")
                        else:
                            self.console.print(f"[yellow]Warning: Required columns not found in {f}. Available: {list(df.columns)}[/yellow]")
                    except Exception as e:
                        self.console.print(f"[red]Error processing MW file {f}: {str(e)}[/red]")

                if df_list:
                    df_concat = pd.concat(df_list, ignore_index=True)
                    mw_unsubscribes = df_concat[df_concat['status'].isin(['unsubscribed', 'blacklisted'])][['email']].copy()
                    mw_unsubscribes.rename(columns={'email': 'Email'}, inplace=True)
                    mw_unsubscribes['Source'] = 'MailWizz_Postpanel'
                    mw_unsubscribes.drop_duplicates(subset='Email', keep='first', inplace=True)
                    self.console.print(f"[green]✓ MailWizz_Postpanel: {len(mw_unsubscribes)} unique unsubscribes[/green]")
                else:
                    self.console.print(f"[yellow]Warning: No valid MW data found[/yellow]")
            else:
                self.console.print(f"[yellow]No CSV files found in {mw_folder}[/yellow]")
        else:
            self.console.print(f"[red]MW folder not found: {mw_folder}[/red]")

        # Process general postpanel data
        pp_folder = r"H:\Master Bounces and Unsubs\Mathews Postpanel Unsubs\postpanel"
        pp_unsubscribes = None

        if os.path.exists(pp_folder):
            os.chdir(pp_folder)
            csv_files = [f for f in glob.glob('*.csv') if not f.startswith('mathews_postpanel_unsubs_concatenated')]
            self.console.print(f"[blue]Found {len(csv_files)} CSV files in postpanel folder[/blue]")

            if csv_files:
                df_list = []
                processed_files = 0
                total_records = 0

                for f in csv_files:
                    try:
                        df = pd.read_csv(f, on_bad_lines='skip', low_memory=False, dtype='unicode')
                        self.console.print(f"[blue]Processing {f}: {len(df)} rows, columns: {list(df.columns)}[/blue]")

                        # Handle different column names more comprehensively
                        email_column = None
                        if 'Email ID' in df.columns:
                            email_column = 'Email ID'
                        elif 'Email' in df.columns:
                            email_column = 'Email'
                        elif 'email' in df.columns:
                            email_column = 'email'
                        elif 'EMAIL' in df.columns:
                            email_column = 'EMAIL'

                        if email_column:
                            df_clean = df[[email_column]].copy()
                            df_clean.rename(columns={email_column: 'Email'}, inplace=True)
                            # Remove empty/null emails
                            df_clean = df_clean.dropna(subset=['Email'])
                            df_clean = df_clean[df_clean['Email'].str.strip() != '']

                            if not df_clean.empty:
                                df_list.append(df_clean)
                                processed_files += 1
                                total_records += len(df_clean)
                                self.console.print(f"[green]✓ Added {len(df_clean)} valid emails from {f}[/green]")
                            else:
                                self.console.print(f"[yellow]Warning: No valid emails found in {f}[/yellow]")
                        else:
                            self.console.print(f"[yellow]Warning: No email column found in {f}. Available columns: {list(df.columns)}[/yellow]")

                    except Exception as e:
                        self.console.print(f"[red]Error processing {f}: {str(e)}[/red]")

                if df_list:
                    pp_unsubscribes = pd.concat(df_list, ignore_index=True)
                    pp_unsubscribes['Source'] = 'General_Postpanel'
                    pp_unsubscribes.drop_duplicates(subset='Email', keep='first', inplace=True)
                    self.console.print(f"[green]✓ General_Postpanel: Processed {processed_files} files, {total_records} total records, {len(pp_unsubscribes)} unique emails[/green]")
                else:
                    self.console.print(f"[yellow]Warning: No valid data found in any postpanel CSV files[/yellow]")
            else:
                self.console.print(f"[yellow]No CSV files found in {pp_folder}[/yellow]")
        else:
            self.console.print(f"[red]Postpanel folder not found: {pp_folder}[/red]")

        return mw_unsubscribes, pp_unsubscribes

    def consolidate_data(self, all_unsubscribes, all_soft_bounces, all_hard_bounces):
        """Consolidate all data into final output files"""
        self.console.print("[yellow]Consolidating all data...[/yellow]")

        # Filter out None values and combine
        valid_unsubscribes = [df for df in all_unsubscribes if df is not None and not df.empty]
        valid_soft_bounces = [df for df in all_soft_bounces if df is not None and not df.empty]
        valid_hard_bounces = [df for df in all_hard_bounces if df is not None and not df.empty]

        # Debug information
        self.console.print(f"[blue]Debug: Found {len(valid_unsubscribes)} valid unsubscribe datasets[/blue]")
        for i, df in enumerate(valid_unsubscribes):
            if 'Source' in df.columns:
                sources = df['Source'].unique()
                self.console.print(f"[blue]  Dataset {i+1}: {len(df)} records from sources: {sources}[/blue]")
            else:
                self.console.print(f"[blue]  Dataset {i+1}: {len(df)} records (no source column)[/blue]")

        # Filter out General_postpanel data from unsubscribes before consolidation
        filtered_unsubscribes = []
        general_postpanel_count = 0

        for df in valid_unsubscribes:
            if 'Source' in df.columns:
                # Check if this dataset contains General_postpanel data
                if 'General_Postpanel' in df['Source'].values:
                    general_postpanel_count += len(df[df['Source'] == 'General_Postpanel'])
                    # Keep only non-General_postpanel data
                    filtered_df = df[df['Source'] != 'General_Postpanel'].copy()
                    if not filtered_df.empty:
                        filtered_unsubscribes.append(filtered_df)
                else:
                    filtered_unsubscribes.append(df)
            else:
                filtered_unsubscribes.append(df)

        if general_postpanel_count > 0:
            self.console.print(f"[yellow]Skipping {general_postpanel_count:,} General_postpanel records from consolidated unsubscribes[/yellow]")

        # Combine all data (excluding General_postpanel)
        if filtered_unsubscribes:
            consolidated_unsubscribes = pd.concat(filtered_unsubscribes, ignore_index=True)
            consolidated_unsubscribes['Email'] = consolidated_unsubscribes['Email'].str.lower()
            consolidated_unsubscribes['ProcessedDate'] = self.date_str
            consolidated_unsubscribes.drop_duplicates(subset='Email', keep='first', inplace=True)
        else:
            consolidated_unsubscribes = pd.DataFrame(columns=['Email', 'Source', 'ProcessedDate'])

        if valid_soft_bounces:
            consolidated_soft_bounces = pd.concat(valid_soft_bounces, ignore_index=True)
            consolidated_soft_bounces['Email'] = consolidated_soft_bounces['Email'].str.lower()
            consolidated_soft_bounces['ProcessedDate'] = self.date_str
            consolidated_soft_bounces.drop_duplicates(subset='Email', keep='first', inplace=True)
        else:
            consolidated_soft_bounces = pd.DataFrame(columns=['Email', 'Source', 'ProcessedDate'])

        if valid_hard_bounces:
            consolidated_hard_bounces = pd.concat(valid_hard_bounces, ignore_index=True)
            consolidated_hard_bounces['Email'] = consolidated_hard_bounces['Email'].str.lower()
            consolidated_hard_bounces['ProcessedDate'] = self.date_str
            consolidated_hard_bounces.drop_duplicates(subset='Email', keep='first', inplace=True)
        else:
            consolidated_hard_bounces = pd.DataFrame(columns=['Email', 'Source', 'ProcessedDate'])

        return consolidated_unsubscribes, consolidated_soft_bounces, consolidated_hard_bounces

    def update_master_hardbounces(self, hard_bounces_df):
        """Update the Master_Hardbounces.csv file with new hard bounce data"""
        self.console.print("[yellow]Updating Master_Hardbounces.csv file...[/yellow]")

        master_hb_path = r"H:\Master Bounces and Unsubs\Master Bounces and Unsubs\Master_Hardbounces.csv"

        try:
            # Check if we have any hard bounces to add
            if hard_bounces_df.empty:
                self.console.print("[yellow]No hard bounces to add to master file[/yellow]")
                return 0

            # Prepare the new hard bounces data (only Email column for master file)
            new_hard_bounces = hard_bounces_df[['Email']].copy()
            new_hard_bounces['Email'] = new_hard_bounces['Email'].str.lower()
            new_hard_bounces.drop_duplicates(subset='Email', keep='first', inplace=True)

            # Load existing master file if it exists
            if os.path.exists(master_hb_path):
                try:
                    df_master_hb = pd.read_csv(master_hb_path, on_bad_lines='skip')
                    self.console.print(f"[green]✓ Loaded existing Master_Hardbounces.csv: {len(df_master_hb)} records[/green]")

                    # Ensure Email column exists and is lowercase
                    if 'Email' in df_master_hb.columns:
                        df_master_hb['Email'] = df_master_hb['Email'].str.lower()
                    else:
                        # If no Email column, create empty dataframe with correct structure
                        df_master_hb = pd.DataFrame(columns=['Email'])

                except Exception as e:
                    self.console.print(f"[red]Error reading master file: {str(e)}[/red]")
                    self.console.print("[yellow]Creating backup and starting fresh...[/yellow]")

                    # Create backup of existing file
                    backup_path = master_hb_path + f".backup_{self.timestamp}"
                    try:
                        import shutil
                        shutil.copy2(master_hb_path, backup_path)
                        self.console.print(f"[green]✓ Backup created: {backup_path}[/green]")
                    except Exception as backup_error:
                        self.console.print(f"[red]Warning: Could not create backup: {str(backup_error)}[/red]")

                    # Start with empty dataframe
                    df_master_hb = pd.DataFrame(columns=['Email'])
            else:
                self.console.print("[yellow]Master_Hardbounces.csv not found, creating new file[/yellow]")
                df_master_hb = pd.DataFrame(columns=['Email'])

            # Count new emails before merging
            existing_emails = set(df_master_hb['Email'].str.lower()) if not df_master_hb.empty else set()
            new_emails = set(new_hard_bounces['Email'].str.lower())
            truly_new_emails = new_emails - existing_emails

            # Combine master and new hard bounces
            df_combined = pd.concat([df_master_hb, new_hard_bounces], ignore_index=True)

            # Remove duplicates (keep first occurrence)
            df_combined.drop_duplicates(subset='Email', keep='first', inplace=True)

            # Save updated master file
            df_combined.to_csv(master_hb_path, index=False, encoding='utf-8-sig')

            self.console.print(f"[green]✓ Master_Hardbounces.csv updated successfully[/green]")
            self.console.print(f"[green]  - Total records in master file: {len(df_combined):,}[/green]")
            self.console.print(f"[green]  - New hard bounces added: {len(truly_new_emails):,}[/green]")

            return len(truly_new_emails)

        except Exception as e:
            self.console.print(f"[red]Error updating Master_Hardbounces.csv: {str(e)}[/red]")
            return 0

    def save_outputs(self, unsubscribes_df, soft_bounces_df, hard_bounces_df):
        """Save consolidated data to different output formats"""
        self.console.print("[yellow]Saving output files...[/yellow]")

        # Create timestamped folder
        output_folder = os.path.join(self.output_path, f"BounceCollection_{self.timestamp}")
        os.makedirs(output_folder, exist_ok=True)

        # Save individual files - Unsubscribes as Excel, others as CSV
        try:
            unsubscribes_df.to_excel(os.path.join(output_folder, "Consolidated_Unsubscribes.xlsx"), index=False, engine='openpyxl')
            self.console.print("[green]✓ Consolidated_Unsubscribes.xlsx saved successfully[/green]")
        except ImportError:
            self.console.print("[yellow]Warning: openpyxl not available, saving as CSV instead[/yellow]")
            unsubscribes_df.to_csv(os.path.join(output_folder, "Consolidated_Unsubscribes.csv"), index=False, encoding='utf-8-sig')

        soft_bounces_df.to_csv(os.path.join(output_folder, "Consolidated_SoftBounces.csv"), index=False, encoding='utf-8-sig')
        hard_bounces_df.to_csv(os.path.join(output_folder, "Consolidated_HardBounces.csv"), index=False, encoding='utf-8-sig')

        # Skip creating summary report, Excel file, and source breakdown as requested
        self.console.print("[yellow]Skipping Processing_Summary.csv, Excel file, and Source_Breakdown.csv as requested[/yellow]")

        # Create ZIP archive
        zip_filename = os.path.join(self.output_path, f"BounceCollection_{self.date_str}.zip")
        with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, _, files in os.walk(output_folder):
                for file in files:
                    file_path = os.path.join(root, file)
                    arcname = os.path.relpath(file_path, output_folder)
                    zipf.write(file_path, arcname)

        return output_folder, zip_filename

    def run_collection(self):
        """Main method to run the entire bounce and unsubscribe collection process"""
        self.display_header("Bounce & Unsubscribe Data Collector v2.0")

        # Archive the bounces folder before processing
        self.console.print("\n[bold yellow]Step 1: Archiving bounces folder...[/bold yellow]")
        archive_path = self.archive_bounces_folder()

        all_unsubscribes = []
        all_soft_bounces = []
        all_hard_bounces = []

        with self.create_progress_bar() as progress:
            # Define processing tasks
            tasks = [
                ("ElasticEmail Bounces", self.process_elasticemail_bounces),
                ("ElasticEmail Unsubscribes", self.process_elasticemail_unsubs),
                ("SendGrid", self.process_sendgrid_data),
                ("Postpanel", self.process_postpanel_data)
            ]

            main_task = progress.add_task("Processing all data sources...", total=len(tasks))

            for task_name, task_func in tasks:
                progress.console.print(f"\n[bold blue]Processing {task_name}...[/bold blue]")

                try:
                    if task_name == "Postpanel":
                        # Postpanel returns different structure
                        mw_unsubs, pp_unsubs = task_func()
                        if mw_unsubs is not None:
                            all_unsubscribes.append(mw_unsubs)
                        if pp_unsubs is not None:
                            all_unsubscribes.append(pp_unsubs)
                    elif task_name == "ElasticEmail Bounces":
                        # ElasticEmail bounces returns (soft_bounces, hard_bounces)
                        soft_b, hard_b = task_func()
                        if soft_b is not None:
                            all_soft_bounces.append(soft_b)
                        if hard_b is not None:
                            all_hard_bounces.append(hard_b)
                    elif task_name == "ElasticEmail Unsubscribes":
                        # ElasticEmail unsubs returns only unsubscribes
                        unsubs = task_func()
                        if unsubs is not None:
                            all_unsubscribes.append(unsubs)
                    else:
                        # SendGrid returns (unsubscribes, soft_bounces, hard_bounces)
                        unsubs, soft_b, hard_b = task_func()
                        if unsubs is not None:
                            all_unsubscribes.append(unsubs)
                        if soft_b is not None:
                            all_soft_bounces.append(soft_b)
                        if hard_b is not None:
                            all_hard_bounces.append(hard_b)

                    progress.console.print(f"[green]✓ {task_name} processed successfully[/green]")

                except Exception as e:
                    progress.console.print(f"[red]✗ Error processing {task_name}: {str(e)}[/red]")

                progress.advance(main_task)

        # Consolidate all data
        self.console.print("\n[bold yellow]Consolidating data from all sources...[/bold yellow]")
        final_unsubscribes, final_soft_bounces, final_hard_bounces = self.consolidate_data(
            all_unsubscribes, all_soft_bounces, all_hard_bounces
        )

        # Save outputs
        self.console.print("\n[bold yellow]Saving consolidated outputs...[/bold yellow]")
        output_folder, zip_file = self.save_outputs(final_unsubscribes, final_soft_bounces, final_hard_bounces)

        # Update Master_Hardbounces.csv file
        self.console.print("\n[bold yellow]Updating Master_Hardbounces.csv file...[/bold yellow]")
        new_hardbounces_added = self.update_master_hardbounces(final_hard_bounces)

        # Display final summary
        archive_info = f"• Source Archive: {archive_path}" if archive_path else "• Source Archive: Failed to create"

        summary_text = f"""
[bold green]Data Collection Complete![/bold green]

[bold]Summary:[/bold]
• Total Unsubscribes: {len(final_unsubscribes):,}
• Total Soft Bounces: {len(final_soft_bounces):,}
• Total Hard Bounces: {len(final_hard_bounces):,}
• Total Records: {len(final_unsubscribes) + len(final_soft_bounces) + len(final_hard_bounces):,}

[bold]Archive Information:[/bold]
{archive_info}

[bold]Master File Updates:[/bold]
• New Hard Bounces Added to Master: {new_hardbounces_added:,}
• Master File Location: H:\\Master Bounces and Unsubs\\Master Bounces and Unsubs\\Master_Hardbounces.csv

[bold]Output Files:[/bold]
• Folder: {output_folder}
• Archive: {zip_file}
• Processing Date: {self.date_str}

[bold]Files Created:[/bold]
• Consolidated_Unsubscribes.xlsx
• Consolidated_SoftBounces.csv
• Consolidated_HardBounces.csv
        """

        self.console.print(Panel(summary_text, title="Results", expand=False))

        return {
            'unsubscribes': final_unsubscribes,
            'soft_bounces': final_soft_bounces,
            'hard_bounces': final_hard_bounces,
            'output_folder': output_folder,
            'zip_file': zip_file,
            'archive_path': archive_path,
            'new_hardbounces_added': new_hardbounces_added
        }


def main():
    """Main execution function"""
    collector = BounceUnsubscribeCollector()
    results = collector.run_collection()
    return results


if __name__ == "__main__":
    main()
